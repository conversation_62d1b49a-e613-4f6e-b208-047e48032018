{"version": "2.0.0", "tasks": [{"label": "Build Mod", "type": "shell", "command": "./gradlew.bat", "args": ["build"], "group": {"kind": "build", "isDefault": true}, "presentation": {"echo": true, "reveal": "always", "focus": false, "panel": "shared"}, "problemMatcher": []}, {"label": "Run Client", "type": "shell", "command": "./gradlew.bat", "args": ["runClient"], "group": "build", "presentation": {"echo": true, "reveal": "always", "focus": false, "panel": "shared"}, "problemMatcher": []}, {"label": "Clean Build", "type": "shell", "command": "./gradlew.bat", "args": ["clean", "build"], "group": "build", "presentation": {"echo": true, "reveal": "always", "focus": false, "panel": "shared"}, "problemMatcher": []}]}