/*
 * CONFIGURAÇÃO DO SISTEMA DE BUILD
 *
 * Este bloco define as ferramentas necessárias para compilar nosso mod.
 * É executado antes de tudo e prepara o ambiente de desenvolvimento.
 */
buildscript {
    // Define onde buscar as dependências do sistema de build
    repositories {
        // Repositório oficial do MinecraftForge - contém o ForgeGradle
        maven { url = 'https://maven.minecraftforge.net' }
        // Repositório central do Maven - contém bibliotecas Java gerais
        mavenCentral()
    }

    // Define as ferramentas necessárias para o build
    dependencies {
        // ForgeGradle - plugin especial que permite compilar mods para Minecraft
        // Versão 5.1.+ é compatível com Java 8 e Minecraft 1.16.5
        // 'changing: true' permite atualizações automáticas da versão
        classpath group: 'net.minecraftforge.gradle', name: 'ForgeGradle', version: '5.1.+', changing: true
    }
}

/*
 * PLUGINS DO PROJETO
 *
 * Plugins são extensões que adicionam funcionalidades ao Gradle.
 * Cada plugin fornece tarefas específicas para nosso projeto.
 */
apply plugin: 'net.minecraftforge.gradle'  // Plugin principal - permite compilar mods
apply plugin: 'eclipse'                    // Suporte para IDE Eclipse (opcional)
apply plugin: 'maven-publish'              // Permite publicar o mod (opcional)

/*
 * INFORMAÇÕES BÁSICAS DO PROJETO
 *
 * Estas informações identificam nosso mod e são usadas para gerar
 * os arquivos finais e metadados.
 */
version = '1.0.0'                          // Versão do nosso mod (aparece no nome do arquivo)
group = 'com.exemplo.meumod'               // Grupo/pacote principal (identificação única)
archivesBaseName = 'meumod'                // Nome base dos arquivos (resultado: meumod-1.0.0.jar)

/*
 * COMPATIBILIDADE JAVA
 *
 * Define qual versão do Java usar para compilar o projeto.
 * Minecraft 1.16.5 foi feito para Java 8, então usamos Java 8.
 * Versões mais novas podem causar problemas de compatibilidade.
 */
sourceCompatibility = targetCompatibility = compileJava.sourceCompatibility = compileJava.targetCompatibility = '1.8'

minecraft {
    mappings channel: 'official', version: '1.16.5'

    runs {
        client {
            workingDirectory project.file('run')
            property 'forge.logging.markers', 'REGISTRIES'
            property 'forge.logging.console.level', 'debug'
            mods {
                meumod {
                    source sourceSets.main
                }
            }
        }

        server {
            workingDirectory project.file('run')
            property 'forge.logging.markers', 'REGISTRIES'
            property 'forge.logging.console.level', 'debug'
            mods {
                meumod {
                    source sourceSets.main
                }
            }
        }

        data {
            workingDirectory project.file('run')
            property 'forge.logging.markers', 'REGISTRIES'
            property 'forge.logging.console.level', 'debug'
            args '--mod', 'meumod', '--all', '--output', file('src/generated/resources/'), '--existing', file('src/main/resources/')
            mods {
                meumod {
                    source sourceSets.main
                }
            }
        }
    }
}

sourceSets.main.resources { srcDir 'src/generated/resources' }

repositories {
    maven {
        name = 'MinecraftForge'
        url = 'https://maven.minecraftforge.net'
    }
}

dependencies {
    minecraft 'net.minecraftforge:forge:1.16.5-36.2.39'
}

// Example for how to get properties into the manifest for reading at runtime.
tasks.named('jar', Jar).configure {
    manifest {
        attributes([
                "Specification-Title"     : "meumod",
                "Specification-Vendor"    : "exemplo",
                "Specification-Version"   : "1", // We are version 1 of ourselves
                "Implementation-Title"    : project.name,
                "Implementation-Version"  : project.jar.archiveVersion,
                "Implementation-Vendor"   : "exemplo",
                "Implementation-Timestamp": new Date().format("yyyy-MM-dd'T'HH:mm:ssZ")
        ])
    }

    // This is the preferred method to reobfuscate your jar file
    finalizedBy 'reobfJar'
}

// However if you are in a multi-project build, dev time needs unobfed jar files, so you can delay the obfuscation until publishing by doing
// tasks.named('publish').configure {
//     dependsOn 'reobfJar'
// }

tasks.named('processResources', ProcessResources).configure {
    var replaceProperties = [
            minecraft_version      : minecraft_version, minecraft_version_range: minecraft_version_range,
            forge_version          : forge_version, forge_version_range: forge_version_range,
            loader_version_range   : loader_version_range,
            mod_id                 : mod_id, mod_name: mod_name, mod_license: mod_license, mod_version: mod_version,
            mod_authors            : mod_authors, mod_description: mod_description,
            version                : version,
    ]
    inputs.properties replaceProperties

    filesMatching(['META-INF/mods.toml']) {
        expand replaceProperties + [project: project]
    }
}

tasks.named('jar', Jar).configure {
    manifest {
        attributes([
            "Specification-Title": "meumod",
            "Specification-Vendor": "exemplo",
            "Specification-Version": "1",
            "Implementation-Title": project.name,
            "Implementation-Version": project.jar.archiveVersion,
            "Implementation-Vendor": "exemplo",
            "Implementation-Timestamp": new Date().format("yyyy-MM-dd'T'HH:mm:ssZ")
        ])
    }
}