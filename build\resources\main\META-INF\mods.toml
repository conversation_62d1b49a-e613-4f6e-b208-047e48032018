# =============================================================================
# ARQUIVO DE CONFIGURAÇÃO DO MOD (FORMATO TOML)
# =============================================================================
#
# Este arquivo contém todas as informações sobre nosso mod que o Forge precisa
# saber para carregá-lo corretamente. É como um "cartão de identidade" do mod.
#
# Formato TOML (Tom's Obvious, Minimal Language) - similar ao INI mas mais moderno
# Campos marcados com #mandatory são obrigatórios
# Campos marcados com #optional são opcionais

# =============================================================================
# CONFIGURAÇÕES DO CARREGADOR
# =============================================================================

# Tipo de carregador de mod a usar
# "javafml" = ForgeModLoader para mods Java (padrão para mods normais)
modLoader="javafml" #mandatory

# Versão do Forge necessária para carregar este mod
# [36,) = substitui pelo valor do gradle.properties
# Esta versão é verificada quando o Minecraft tenta carregar o mod
loaderVersion="[36,)" #mandatory
# Licença do mod (obrigatório para distribuição)
# MIT = substitui pelo valor do gradle.properties (MIT)
# Ajuda outros desenvolvedores a entender como podem usar seu mod
license="MIT"

# URL para reportar problemas com o mod (opcional)
# Descomentado quando você tiver um repositório GitHub ou sistema de issues
#issueTrackerURL="https://change.me.to.your.issue.tracker.example.invalid/" #optional

# =============================================================================
# LISTA DE MODS (ESTE ARQUIVO PODE DEFINIR MÚLTIPLOS MODS)
# =============================================================================

# Início da definição do nosso mod (obrigatório)
# [[mods]] cria uma nova entrada na lista de mods
[[mods]] #mandatory

# ID único do mod (deve corresponder ao MODID na classe Java)
# meumod = substitui pelo valor do gradle.properties (meumod)
modId="meumod" #mandatory

# Versão do mod (obrigatório)
# 1.0.0 = substitui pelo valor do gradle.properties (1.0.0)
# Também pode usar 1.0.0 que pega do JAR metadata
version="1.0.0" #mandatory

# Nome bonito do mod que aparece na interface
# Meu Primeiro Mod = substitui pelo valor do gradle.properties
displayName="Meu Primeiro Mod" #mandatory
# A URL to query for updates for this mod. See the JSON update specification https://docs.minecraftforge.net/en/latest/misc/updatechecker/
#updateJSONURL="https://change.me.example.invalid/updates.json" #optional
# A URL for the "homepage" for this mod, displayed in the mod UI
#displayURL="https://change.me.to.your.mods.homepage.example.invalid/" #optional
# A file name (in the root of the mod JAR) containing a logo for display
logoFile="meumod.png" #optional
# A text field displayed in the mod UI
credits="Thanks for this example mod goes to Java" #optional
# A text field displayed in the mod UI
authors="SeuNome" #optional
# Display Test controls the display for your mod in the server connection screen
# MATCH_VERSION means that your mod will cause a red X if the versions on client and server differ. This is the default behaviour and should be what you choose if you have server and client elements to your mod.
# IGNORE_SERVER_VERSION means that your mod will not cause a red X if it's present on the server but not on the client. This is what you should use if you're a server only mod.
# IGNORE_ALL_VERSION means that your mod will not cause a red X if it's present on the client or the server. This is a special case and should only be used if your mod has no server component.
# NONE means that no display test is set on your mod. You need to do this yourself, see IExtensionPoint.DisplayTest for more information. You can define any scheme you wish with this value.
# IMPORTANT NOTE: this is NOT an instruction as to which environments (CLIENT or DEDICATED SERVER) your mod loads on. Your mod should load (and maybe do nothing!) whereever it finds itself.
#displayTest="MATCH_VERSION" # MATCH_VERSION is the default if nothing is specified (#optional)

# The description text for the mod (multi line allowed)
description='''Um mod básico que adiciona uma Ruby ao Minecraft'''

# A dependency - use the . to indicate dependency for a specific modid. Dependencies are optional.
[[dependencies.meumod]] #optional
    # the modid of the dependency
    modId="forge" #mandatory
    # Does this dependency have to exist - if not, ordering below must be specified
    mandatory=true #mandatory
    # The version range of the dependency
    versionRange="[36,)" #mandatory
    # An ordering relationship for the dependency - BEFORE or AFTER required if the relationship is not mandatory
    ordering="NONE"
    # Side this dependency is applied on - BOTH, CLIENT or SERVER
    side="BOTH"
# Here's another dependency
[[dependencies.meumod]]
    modId="minecraft"
    mandatory=true
# This version range declares a minimum of the current minecraft version up to but not including the next major version
    versionRange="[1.16.5,1.17)"
    ordering="NONE"
    side="BOTH"