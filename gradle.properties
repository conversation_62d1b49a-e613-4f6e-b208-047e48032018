# =============================================================================
# CONFIGURAÇÕES DE MEMÓRIA DO GRADLE
# =============================================================================

# Define a memória padrão usada pelos comandos do Gradle
# Isso é necessário para fornecer memória suficiente para o processo de
# descompilação do Minecraft (que é muito pesado)
# -Xmx1G = máximo 1GB de RAM
# -Xms512M = mínimo 512MB de RAM
# -XX:MaxMetaspaceSize=256M = máximo 256MB para metadados de classes
org.gradle.jvmargs=-Xmx1G -Xms512M -XX:MaxMetaspaceSize=256M

# Desabilita o daemon do Gradle para evitar problemas de memória
# O daemon mantém o Gradle rodando em segundo plano, mas pode causar problemas
org.gradle.daemon=false

# =============================================================================
# CONFIGURAÇÕES ESPECÍFICAS PARA MCP (MOD CODER PACK)
# =============================================================================

# MCP é o sistema que traduz o código ofuscado do Minecraft para código legível
# Estas configurações reduzem a memória para evitar problemas com Java 8 32-bit
mcp.jvmargs=-Xmx1G -Xms512M

# =============================================================================
# PROPRIEDADES DO AMBIENTE DE DESENVOLVIMENTO
# =============================================================================

# Versão do Minecraft que nosso mod suporta
# Deve ser compatível com a versão do Forge escolhida
minecraft_version=1.16.5

# Intervalo de versões do Minecraft suportadas pelo mod
# [1.16.5,1.17) = da versão 1.16.5 até 1.17 (não incluindo 1.17)
minecraft_version_range=[1.16.5,1.17)

# Versão específica do Forge que usamos para desenvolver
# Deve ser compatível com a versão do Minecraft
forge_version=36.2.39

# Intervalo de versões do Forge suportadas pelo mod
# [36,) = versão 36 ou superior (sem limite superior)
forge_version_range=[36,)

# Intervalo de versões do carregador de mods (Forge Loader)
# Controla qual versão mínima do Forge é necessária para carregar o mod
loader_version_range=[36,)

# Canal de mapeamentos para traduzir código ofuscado
# "official" = mapeamentos oficiais da Mojang (mais estáveis)
# Outras opções: "parchment_snapshots", "parchment_releases", "crowdin"
mapping_channel=official

# Versão dos mapeamentos a usar
# Deve corresponder à versão do Minecraft
mapping_version=1.16.5


# =============================================================================
# PROPRIEDADES DO MOD
# =============================================================================

# Identificador único do mod (deve ser igual ao MODID na classe principal)
# Regras: apenas letras minúsculas, números e underscore
# Deve corresponder ao valor em MeuMod.MODID
mod_id=meumod

# Nome bonito do mod que aparece na lista de mods
mod_name=Meu Primeiro Mod

# Licença do mod (obrigatório para distribuição)
# MIT = licença permissiva, permite uso comercial e modificação
# Outras opções: GPL, Apache, All Rights Reserved, etc.
mod_license=MIT

# Versão do mod (segue padrão semântico: MAJOR.MINOR.PATCH)
# 1.0.0 = primeira versão estável
mod_version=1.0.0

# ID do grupo para publicação em repositórios Maven
# Deve corresponder ao pacote base do código Java
mod_group_id=com.exemplo.meumod

# Autor(es) do mod (aparece na lista de mods)
mod_authors=SeuNome

# Descrição do mod (aparece na lista de mods)
# Pode ter múltiplas linhas
mod_description=Um mod básico que adiciona uma Ruby ao Minecraft

# =============================================================================
# PROPRIEDADE PARA EXPANSÃO DE TEMPLATES
# =============================================================================

# Versão usada para substituir ${version} em outros arquivos
# Deve ser igual a mod_version
version=1.0.0
