{"_comment": "=============================================================================", "_comment2": "ARQUIVO DE TRADUÇÃO - PORTUGUÊS BRASILEIRO (pt_br)", "_comment3": "=============================================================================", "_comment4": "Este arquivo define como os textos do mod aparecem em português no jogo.", "_comment5": "Formato JSON: 'chave.de.tradução': 'Texto que aparece no jogo'", "_comment6": "", "_comment7": "TRADUÇÃO DE ITENS", "_comment8": "Formato da chave: item.[modid].[nome_interno_do_item]", "_comment9": "O nome interno deve corresponder ao usado no registro (ModItems.java)", "item.meumod.ruby": "Rubi", "_comment10": "TRADUÇÃO DE GRUPOS DE ITENS (ABAS DO INVENTÁRIO)", "_comment11": "Usado se criarmos um grupo personalizado de itens", "_comment12": "Por enquanto usamos ItemGroup.TAB_MISC (aba Diversos padrão)", "itemGroup.meumod": "<PERSON><PERSON>", "_comment13": "EXEMPLOS DE OUTRAS TRADUÇÕES POSSÍVEIS:", "_comment14": "Blocos: 'block.meumod.nome_bloco': '<PERSON><PERSON>'", "_comment15": "Entidades: 'entity.meumod.nome_entidade': 'Nome <PERSON>ito'", "_comment16": "Efeitos: 'effect.meumod.nome_efeito': 'Nome <PERSON>'", "_comment17": "Mensagens: 'message.meumod.alguma_mensagem': 'Texto da mensagem'"}