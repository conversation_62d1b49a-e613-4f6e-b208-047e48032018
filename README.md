# 🎮 Meu Primeiro Mod - Minecraft 1.16.5

Um mod simples que adiciona um item Ruby ao Minecraft, criado para aprender desenvolvimento de mods com MinecraftForge.

**🎓 PROJETO EDUCACIONAL TOTALMENTE COMENTADO EM PORTUGUÊS**

Todos os arquivos de código estão comentados linha por linha em português para facilitar o aprendizado!

## 📋 O que o Mod Adiciona

- **💎 Item Ruby**: Uma bela gema vermelha que aparece na aba "Diversos" do inventário criativo
- **🎨 Textura Personalizada**: Textura 16x16 com design de gema brilhante criada programaticamente
- **🌍 Traduções**: Suporte completo para português brasileiro e inglês
- **📚 Documentação**: Código totalmente comentado para aprendizado

## 🛠️ Estrutura do Projeto (Comentada)

### **📁 Código Java (Totalmente Comentado)**
```
src/main/java/com/exemplo/meumod/
├── MeuMod.java              # 🎯 Classe principal do mod (COMENTADA)
└── items/
    └── ModItems.java        # 💎 Definição dos itens (COMENTADA)
```

### **📁 Recursos (Assets) - Todos Comentados**
```
src/main/resources/
├── META-INF/
│   └── mods.toml           # ⚙️ Configurações do mod (COMENTADO)
├── pack.mcmeta             # 📦 Metadados do resource pack (COMENTADO)
└── assets/meumod/
    ├── lang/
    │   ├── en_us.json      # 🇺🇸 Traduções em inglês (COMENTADO)
    │   └── pt_br.json      # 🇧🇷 Traduções em português (COMENTADO)
    ├── models/item/
    │   └── ruby.json       # 🎨 Modelo 3D do item Ruby (COMENTADO)
    └── textures/item/
        └── ruby.png        # 🖼️ Textura do item Ruby (16x16)
```

### **📁 Configuração (Totalmente Comentada)**
```
├── build.gradle            # ⚙️ Configuração de build (COMENTADO)
├── gradle.properties       # 🔧 Propriedades do Gradle (COMENTADO)
├── README.md              # 📖 Esta documentação
└── .vscode/               # 💻 Configurações do VS Code
    ├── settings.json      # ⚙️ Configurações do Java/Gradle
    ├── tasks.json         # 🔨 Tarefas de build
    └── launch.json        # 🐛 Configuração de debug
```

## 🚀 Como Compilar

1. **Pré-requisitos:**
   - Java 8 JDK instalado
   - JAVA_HOME configurado

2. **Compilar o mod:**
   ```bash
   ./gradlew.bat clean build
   ```

3. **Arquivo gerado:**
   - `build/libs/meumod-1.0.0.jar`

## 📦 Como Instalar

1. Instale **Minecraft Forge 1.16.5** (versão 36.2.39 ou superior)
2. Copie `meumod-1.0.0.jar` para a pasta `%appdata%\.minecraft\mods\`
3. Execute o Minecraft com o perfil Forge

## 🎯 Como Encontrar o Item

1. Entre no **modo criativo** (`/gamemode creative`)
2. Abra o **inventário** (tecla E)
3. Procure na aba **"Diversos"** ou busque por `ruby`
4. Ou use o comando: `/give @s meumod:ruby`

## 📚 Conceitos Aprendidos

### **Sistema de Registro do Forge**
- **DeferredRegister**: Registro seguro de itens
- **RegistryObject**: Referência segura a objetos registrados
- **ForgeRegistries**: Registros principais do Minecraft

### **Estrutura de Mods**
- **@Mod**: Anotação que marca a classe principal
- **MODID**: Identificador único do mod
- **Event Bus**: Sistema de eventos do Forge

### **Resource Packs**
- **Modelos JSON**: Definição de aparência 3D
- **Texturas PNG**: Imagens dos itens
- **Traduções**: Suporte a múltiplos idiomas

### **Build System**
- **Gradle**: Sistema de build automático
- **ForgeGradle**: Plugin específico para mods
- **Mappings**: Tradução de código ofuscado

## 🔧 Comandos Úteis

```bash
# Compilar o mod
./gradlew.bat build

# Executar cliente de desenvolvimento
./gradlew.bat runClient

# Limpar arquivos de build
./gradlew.bat clean

# Compilar e executar
./gradlew.bat clean build runClient
```

## 📖 Próximos Passos

Para expandir este mod, você pode:

1. **Adicionar mais itens** (espadas, armaduras, blocos)
2. **Criar receitas de crafting** para o Ruby
3. **Adicionar funcionalidades** (poderes especiais, etc.)
4. **Criar blocos personalizados**
5. **Adicionar mobs ou estruturas**

## 🎓 Recursos para Aprender Mais

- [Documentação oficial do MinecraftForge](https://docs.minecraftforge.net/)
- [Mappings MCP](https://linkie.shedaniel.me/mappings) - Para entender nomes de classes
- [Forge Community Wiki](https://forge.gemwire.uk/wiki/Main_Page)
- [Exemplos de código](https://github.com/MinecraftForge/MinecraftForge/tree/1.16.x/src/test/java/net/minecraftforge/debug)

---

**Parabéns! Você criou seu primeiro mod para Minecraft! 🎉**
