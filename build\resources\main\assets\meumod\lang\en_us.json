{"_comment": "=============================================================================", "_comment2": "TRANSLATION FILE - ENGLISH (en_us)", "_comment3": "=============================================================================", "_comment4": "This file defines how mod texts appear in English in the game.", "_comment5": "JSON format: 'translation.key': 'Text that appears in game'", "_comment6": "", "_comment7": "ITEM TRANSLATIONS", "_comment8": "Key format: item.[modid].[internal_item_name]", "_comment9": "Internal name must match the one used in registration (ModItems.java)", "item.meumod.ruby": "<PERSON>", "_comment10": "ITEM GROUP TRANSLATIONS (CREATIVE INVENTORY TABS)", "_comment11": "Used if we create a custom item group", "_comment12": "Currently we use ItemGroup.TAB_MISC (default Miscellaneous tab)", "itemGroup.meumod": "My Mod", "_comment13": "EXAMPLES OF OTHER POSSIBLE TRANSLATIONS:", "_comment14": "Blocks: 'block.meumod.block_name': 'Pretty Name'", "_comment15": "Entities: 'entity.meumod.entity_name': 'Pretty Name'", "_comment16": "Effects: 'effect.meumod.effect_name': 'Pretty Name'", "_comment17": "Messages: 'message.meumod.some_message': 'Message text'"}