{"java.configuration.updateBuildConfiguration": "automatic", "java.compile.nullAnalysis.mode": "disabled", "java.import.gradle.enabled": true, "java.import.gradle.wrapper.enabled": true, "java.import.gradle.java.home": "C:\\Program Files (x86)\\Java\\jdk-1.8", "java.configuration.runtimes": [{"name": "JavaSE-1.8", "path": "C:\\Program Files (x86)\\Java\\jdk-1.8"}], "java.jdt.ls.vmargs": "-XX:+UseParallelGC -XX:GCTimeRatio=4 -XX:AdaptiveSizePolicyWeight=90 -Dsun.zip.disableMemoryMapping=true -Xmx1G -Xms100m", "java.import.gradle.offline.enabled": false, "java.sources.organizeImports.starThreshold": 99, "java.sources.organizeImports.staticStarThreshold": 99, "java.completion.enabled": true, "java.signatureHelp.enabled": true, "java.contentProvider.preferred": "fernflower", "java.import.gradle.arguments": "--stacktrace --info"}